import { FC, useCallback, useEffect, useMemo } from 'react';
import { Checkbox } from 'antd';
import { motion } from 'framer-motion';
import moment from 'moment';
import {
    useAccount,
    useConnect,
    useDisconnect,
    useSwitchChain,
    useChainId,
} from 'wagmi';
import * as amplitude from '@amplitude/analytics-browser';

import { Icons } from '@assets';
import { iconFromWalletName } from '@constants';
import { useBalance } from '@entities/balance';
import { useBottomSheetStore } from '@entities/bottom-sheet';
import { useCoreStore } from '@entities/core';
import { useSelectedWalletStore } from '@entities/wallet/model';
import { Deposit } from '@features/deposit';
import { useEmbeddedWallet } from '@hooks';
import { Button, Title, Typography } from '@ui';
import { shortAddress } from '@utils';

import styles from './select-wallet.module.scss';

export const SelectWallet: FC<{ onSelect: (isEmbedded: boolean) => void }> = ({
    onSelect,
}) => {
    const embeddedWallet = useEmbeddedWallet();
    const { address, isConnected, connector } = useAccount();
    const { connect, connectors } = useConnect();
    const { disconnect } = useDisconnect();
    const { switchChain } = useSwitchChain();
    const chainId = useChainId();

    const setIsEmbedded = useSelectedWalletStore(
        (store) => store.setIsEmbedded,
    );
    const isEmbedded = useSelectedWalletStore((store) => store.isEmbedded);

    const { userId, isTma } = useCoreStore();

    // Get target chain ID based on environment
    const getTargetChainId = useCallback(() => {
        const chainId = process.env.NEXT_PUBLIC_CHAIN_ID;
        const rpcUrl = process.env.NEXT_PUBLIC_RPC_URL;

        // Use chain ID if explicitly set
        if (chainId) {
            return parseInt(chainId, 10);
        }

        // Fallback: If RPC_URL is set to localhost, target anvil chain (31337)
        if (rpcUrl && rpcUrl.includes('localhost')) {
            return 1337;
        }

        // Default: target mainnet (1) or sepolia (********) based on NODE_ENV
        return process.env.NODE_ENV === 'production' ? 1 : ********;
    }, []);

    const targetChainId = getTargetChainId();

    // Auto-switch network when wallet is connected and on wrong network
    useEffect(() => {
        if (
            isConnected &&
            !isEmbedded &&
            chainId !== targetChainId &&
            switchChain
        ) {
            const switchNetwork = async () => {
                try {
                    await switchChain({ chainId: targetChainId });

                    amplitude.logEvent('Network_Switch_Success', {
                        userId,
                        fromChainId: chainId,
                        toChainId: targetChainId,
                        timestamp: moment().unix(),
                    });
                } catch (error) {
                    console.error('Failed to switch network:', error);

                    amplitude.logEvent('Network_Switch_Error', {
                        userId,
                        fromChainId: chainId,
                        toChainId: targetChainId,
                        error:
                            error instanceof Error
                                ? error.message
                                : 'Unknown error',
                        timestamp: moment().unix(),
                    });

                    // Show user-friendly error message
                    alert(
                        `Please switch to the correct network manually. Expected network ID: ${targetChainId}, current: ${chainId}`,
                    );
                }
            };

            switchNetwork();
        }
    }, [isConnected, isEmbedded, chainId, targetChainId, switchChain, userId]);

    useEffect(() => {
        if (userId) {
            amplitude.logEvent('View_WalletConnectPage', {
                userId,
                timestamp: moment().unix(),
            });
        }
    }, [userId]);

    const handleSelect = useCallback(
        (isEmbedded: boolean) => () => {
            setIsEmbedded(isEmbedded);
            onSelect(isEmbedded);

            if (isEmbedded) {
                if (!embeddedWallet.wallet?.address) {
                    amplitude.logEvent('Clicking_EmbeddedWallet_Creation', {
                        userId,
                        timestamp: moment().unix(),
                    });

                    embeddedWallet.createWallet();
                }
            } else {
                if (!isConnected) {
                    amplitude.logEvent('Clicking_MetaMaskWallet_Connect', {
                        userId,
                        timestamp: moment().unix(),
                    });

                    alert(
                        'Use only testnet ETH, not the real ETH to trade! DO NOT use mainnet ETH or any other mainnet assets with SODA embedded wallet. If you send mainnet tokens, you will lose them without any chance to recover.',
                    );

                    try {
                        const metamaskConnector = connectors.find(
                            (c) => c.name === 'MetaMask',
                        );
                        if (metamaskConnector) {
                            connect({ connector: metamaskConnector });
                        }
                    } catch (error) {
                        amplitude.logEvent('Error_MetaMaskWallet_Connect', {
                            userId,
                            timestamp: moment().unix(),
                        });
                    }
                }
            }
        },
        [
            setIsEmbedded,
            onSelect,
            embeddedWallet,
            isConnected,
            connect,
            connectors,
            userId,
        ],
    );

    const { balance: embeddedBalance } = useBalance(
        embeddedWallet.wallet?.address,
    );

    const show = useBottomSheetStore((store) => store.show);

    const handleTopUp = useCallback(
        (e: React.MouseEvent<HTMLButtonElement>) => {
            e.stopPropagation();

            alert(
                'Use only testnet ETH, not the real ETH to trade! DO NOT use mainnet ETH or any other mainnet assets with SODA embedded wallet. If you send mainnet tokens, you will lose them without any chance to recover.',
            );

            show({
                title: 'Deposit ETH',
                description: 'Send any amount on your ETH wallet',
                customContent: <Deposit />,
            });
        },
        [show],
    );

    const embeddedWalletContent = useMemo(() => {
        if (!embeddedWallet.wallet?.address) {
            return (
                <motion.div
                    onClick={handleSelect(true)}
                    className={styles.item}
                    whileTap={{
                        scale: 0.97,
                    }}
                >
                    <div className={styles.itemIcon}>
                        <Icons.Plus
                            width={24}
                            height={24}
                            color="var(--color-fg-tertiary)"
                        />
                    </div>

                    <div className={styles.itemContent}>
                        <Typography size="l">Create Embedded Wallet</Typography>
                    </div>

                    <Icons.AngleRight
                        width={24}
                        height={24}
                        color="var(--color-fg-tertiary)"
                    />
                </motion.div>
            );
        }

        return (
            <motion.div
                onClick={handleSelect(true)}
                className={styles.wallet}
                whileTap={{
                    scale: 0.97,
                }}
            >
                <div className={styles.walletHeader}>
                    <div
                        style={{
                            display: 'flex',
                            alignItems: 'center',
                            gap: 8,
                        }}
                    >
                        {iconFromWalletName.Embedded}

                        <Typography weight="500" size="l">
                            Embedded Wallet
                        </Typography>
                    </div>

                    <Checkbox checked={isEmbedded} />
                </div>

                <div className={styles.walletAddress}>
                    <Typography size="m" variant="tertiary">
                        Address
                    </Typography>

                    <Typography weight="600" size="l" variant="primary">
                        {shortAddress(embeddedWallet.wallet.address)}
                    </Typography>
                </div>

                <div className={styles.walletFooter}>
                    <div className={styles.walletBalance}>
                        <Typography size="m" variant="tertiary">
                            Balance
                        </Typography>

                        <div
                            style={{
                                display: 'flex',
                                alignItems: 'center',
                                gap: 6,
                            }}
                        >
                            <Title weight="600" size="s" variant="primary">
                                {Intl.NumberFormat('en-US', {
                                    style: 'decimal',
                                }).format(embeddedBalance)}
                            </Title>

                            <Icons.Eth width={20} height={20} />
                        </div>
                    </div>

                    <Button onClick={handleTopUp}>Top up</Button>
                </div>
            </motion.div>
        );
    }, [
        embeddedWallet,
        embeddedBalance,
        isEmbedded,
        handleSelect,
        handleTopUp,
    ]);

    const { balance: externalBalance } = useBalance(address);

    const externalWalletContent = useMemo(() => {
        if (!isConnected || !address) {
            return (
                <motion.div
                    onClick={handleSelect(false)}
                    whileTap={{
                        scale: 0.97,
                    }}
                    className={styles.item}
                >
                    <div className={styles.itemIcon}>
                        <Icons.Eth width={24} height={24} />
                    </div>

                    <div className={styles.itemContent}>
                        <Typography size="l">
                            Connect MetaMask wallet
                        </Typography>

                        <Typography size="s" variant="secondary">
                            Authorize your wallet in Soda
                        </Typography>
                    </div>

                    <Icons.AngleRight
                        width={24}
                        height={24}
                        color="var(--color-fg-tertiary)"
                    />
                </motion.div>
            );
        }

        return (
            <motion.div
                onClick={handleSelect(false)}
                className={styles.wallet}
                whileTap={{
                    scale: 0.97,
                }}
            >
                <div className={styles.walletHeader}>
                    <div
                        style={{
                            display: 'flex',
                            alignItems: 'center',
                            gap: 8,
                        }}
                    >
                        {(connector?.name &&
                            iconFromWalletName[connector.name]) ||
                            iconFromWalletName.MetaMask}

                        <Typography weight="500" size="l">
                            {connector?.name || 'MetaMask'}
                        </Typography>
                    </div>

                    <Checkbox checked={!isEmbedded} />
                </div>

                <div className={styles.walletAddress}>
                    <Typography size="m" variant="tertiary">
                        Address
                    </Typography>

                    <Typography weight="600" size="l" variant="primary">
                        {shortAddress(address)}
                    </Typography>
                </div>

                <div className={styles.walletFooter}>
                    <div className={styles.walletBalance}>
                        <Typography size="m" variant="tertiary">
                            Balance
                        </Typography>

                        <div
                            style={{
                                display: 'flex',
                                alignItems: 'center',
                                gap: 6,
                            }}
                        >
                            <Title weight="600" size="s" variant="primary">
                                {Intl.NumberFormat('en-US', {
                                    style: 'decimal',
                                }).format(externalBalance)}
                            </Title>

                            <Icons.Eth width={20} height={20} />
                        </div>
                    </div>

                    <Button onClick={() => disconnect()}>Disconnect</Button>
                </div>
            </motion.div>
        );
    }, [
        isConnected,
        address,
        connector,
        externalBalance,
        isEmbedded,
        handleSelect,
        disconnect,
    ]);

    return (
        <div>
            {isTma && embeddedWalletContent}
            {externalWalletContent}
        </div>
    );
};
