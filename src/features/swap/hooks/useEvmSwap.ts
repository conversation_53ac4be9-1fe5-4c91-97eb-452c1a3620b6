'use client';
import { useCallback, useMemo, useEffect } from 'react';
import {
    useAccount,
    useWriteContract,
    useWaitForTransactionReceipt,
    useReadContract,
} from 'wagmi';
import { parseEther, parseUnits, maxUint256 } from 'viem';
import { useNotificationsAdd } from '@entities/notifications';
import { getSodaFactoryAddress } from '@shared/constants/ethereum';
import SodaFactoryABI from '@shared/abi/SodaFactoryABI.json';
import SodaTokenABI from '@shared/abi/SodaTokenABI.json';

interface UseEvmSwapParams {
    tokenAddress: string | null;
    amount: string;
    tradeType: 'buy' | 'sell';
    enabled?: boolean;
}

interface SwapResult {
    executeSwap: () => Promise<void>;
    isLoading: boolean;
    isSuccess: boolean;
    error: Error | null;
    needsApproval: boolean;
    executeApproval: () => Promise<void>;
    isApproving: boolean;
    isApprovalSuccess: boolean;
    approvalError: Error | null;
}

export const useEvmSwap = ({
    tokenAddress,
    amount,
    tradeType,
    enabled = true,
}: UseEvmSwapParams): SwapResult => {
    const { address: userAddress } = useAccount();
    const addNotification = useNotificationsAdd();
    const factoryAddress = getSodaFactoryAddress();

    // Contract write hooks
    const {
        writeContract: writeSwap,
        data: swapHash,
        isPending: isSwapPending,
        error: swapError,
    } = useWriteContract();

    const {
        writeContract: writeApproval,
        data: approvalHash,
        isPending: isApprovalPending,
        error: approvalError,
    } = useWriteContract();

    // Transaction receipt hooks
    const { isLoading: isSwapConfirming, isSuccess: isSwapSuccess } =
        useWaitForTransactionReceipt({
            hash: swapHash,
        });

    const { isLoading: isApprovalConfirming, isSuccess: isApprovalSuccess } =
        useWaitForTransactionReceipt({
            hash: approvalHash,
        });

    // Check current allowance for sell operations
    const { data: currentAllowance, refetch: refetchAllowance } =
        useReadContract({
            address: tokenAddress as `0x${string}`,
            abi: SodaTokenABI.abi,
            functionName: 'allowance',
            args: [userAddress, factoryAddress],
            query: {
                enabled:
                    enabled &&
                    tradeType === 'sell' &&
                    !!tokenAddress &&
                    !!factoryAddress &&
                    !!userAddress,
            },
        });

    // Calculate if approval is needed
    const needsApproval = useMemo(() => {
        if (tradeType !== 'sell' || !amount || !currentAllowance) {
            console.log('Approval check - early return:', {
                tradeType,
                amount,
                currentAllowance: currentAllowance?.toString(),
            });
            return false;
        }

        try {
            const amountBigInt = parseUnits(amount, 18);
            const needsApprovalResult =
                (currentAllowance as bigint) < amountBigInt;

            console.log('Allowance check:', {
                tokenAddress,
                userAddress,
                factoryAddress,
                currentAllowance: currentAllowance.toString(),
                requiredAmount: amountBigInt.toString(),
                needsApproval: needsApprovalResult,
            });

            return needsApprovalResult;
        } catch (error) {
            console.error('Error calculating approval need:', error);
            return false;
        }
    }, [
        tradeType,
        amount,
        currentAllowance,
        tokenAddress,
        userAddress,
        factoryAddress,
    ]);

    // Execute approval
    const executeApproval = useCallback(async () => {
        if (!tokenAddress || !factoryAddress || !userAddress) {
            console.error('Missing required addresses for approval:', {
                tokenAddress,
                factoryAddress,
                userAddress,
            });
            addNotification('error', 'Missing required addresses for approval');
            return;
        }

        console.log('Executing approval:', {
            tokenAddress,
            spender: factoryAddress,
            amount: maxUint256.toString(),
            userAddress,
        });

        try {
            await writeApproval({
                address: tokenAddress as `0x${string}`,
                abi: SodaTokenABI.abi,
                functionName: 'approve',
                args: [factoryAddress as `0x${string}`, maxUint256],
            });

            console.log('Approval transaction submitted successfully');
            addNotification('success', 'Approval transaction submitted');
        } catch (error) {
            console.error('Approval failed:', error);
            addNotification('error', 'Approval transaction failed');
        }
    }, [
        tokenAddress,
        factoryAddress,
        userAddress,
        writeApproval,
        addNotification,
    ]);

    // Execute swap
    const executeSwap = useCallback(async () => {
        if (!tokenAddress || !factoryAddress || !userAddress || !amount) {
            console.error('Missing required parameters for swap:', {
                tokenAddress,
                factoryAddress,
                userAddress,
                amount,
            });
            addNotification('error', 'Missing required parameters for swap');
            return;
        }

        // For sell operations, check if approval is needed first
        if (tradeType === 'sell' && needsApproval) {
            console.error('Cannot execute sell: approval required first');
            addNotification('error', 'Please approve token spending first');
            return;
        }

        try {
            if (tradeType === 'buy') {
                // Buy tokens with ETH
                const ethAmount = parseEther(amount);

                console.log('Executing buy:', {
                    tokenAddress,
                    ethAmount: ethAmount.toString(),
                    factoryAddress,
                });

                await writeSwap({
                    address: factoryAddress as `0x${string}`,
                    abi: SodaFactoryABI.abi,
                    functionName: 'buyExactIn',
                    args: [tokenAddress as `0x${string}`, 0n], // _amountOutMin = 0
                    value: ethAmount,
                });
            } else {
                // Sell tokens for ETH
                const tokenAmount = parseUnits(amount, 18);

                console.log('Executing sell:', {
                    tokenAddress,
                    tokenAmount: tokenAmount.toString(),
                    factoryAddress,
                    currentAllowance: currentAllowance?.toString(),
                });

                await writeSwap({
                    address: factoryAddress as `0x${string}`,
                    abi: SodaFactoryABI.abi,
                    functionName: 'sellExactIn',
                    args: [
                        tokenAddress as `0x${string}`,
                        tokenAmount,
                        0n, // _amountCollateralMin = 0
                    ],
                });
            }

            console.log('Swap transaction submitted successfully');
        } catch (error) {
            console.error('Swap failed:', error);
            addNotification('error', 'Swap transaction failed');
        }
    }, [
        tokenAddress,
        factoryAddress,
        userAddress,
        amount,
        tradeType,
        writeSwap,
        addNotification,
        needsApproval,
        currentAllowance,
    ]);

    // Refetch allowance after successful approval
    useEffect(() => {
        if (isApprovalSuccess) {
            console.log('Approval successful, refetching allowance...');
            refetchAllowance();
        }
    }, [isApprovalSuccess, refetchAllowance]);

    return {
        executeSwap,
        isLoading: isSwapPending || isSwapConfirming,
        isSuccess: isSwapSuccess,
        error: swapError as Error | null,
        needsApproval,
        executeApproval,
        isApproving: isApprovalPending || isApprovalConfirming,
        isApprovalSuccess,
        approvalError: approvalError as Error | null,
    };
};
