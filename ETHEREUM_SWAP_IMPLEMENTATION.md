# Ethereum Token Swap Implementation

This document describes the implementation of Ethereum token swap functionality using the SodaToken contract's `getAmountOut` function.

## Overview

The implementation adds support for calculating correct swap amounts when trading tokens on Ethereum (as opposed to Solana). It uses the `getAmountOut` function from the SodaToken smart contract to provide accurate price quotes.

## Key Components

### 1. useEthereumAmountOut Hook

**Location**: `src/features/swap/hooks/useEthereumAmountOut.ts`

This hook handles calling the `getAmountOut` function from the SodaToken contract using viem.

**Parameters**:

- `contractAddress`: The SodaToken contract address
- `amount`: The input amount (in ETH for buy, in tokens for sell)
- `tradeType`: Either 'buy' or 'sell'
- `enabled`: Whether the hook should be active

**Returns**:

- `amountOut`: Raw amount out from the contract
- `fee`: Fee amount from the contract
- `netAmountOut`: For sell trades, this is `amountOut - fee`
- `isLoading`: Loading state
- `error`: Any errors that occurred

### 2. Contract Function Usage

The hook calls the `getAmountOut` function with the following logic:

#### For Buying Tokens (ETH → Tokens)

```typescript
// Call: getAmountOut(amountIn, true)
// amountIn: ETH amount user wants to spend
// _collateralTokenIsIn: true (ETH is the input)
// Returns: [tokensOut, fee]
// User receives: tokensOut tokens
```

#### For Selling Tokens (Tokens → ETH)

```typescript
// Call: getAmountOut(tokensToSell, false)
// amountIn: Token amount user wants to sell
// _collateralTokenIsIn: false (tokens are the input)
// Returns: [ethAmountOut, sellFee]
// User receives: ethAmountOut - sellFee ETH
```

### 3. Integration in Swap Component

**Location**: `src/features/swap/ui/swap.tsx`

The swap component now:

1. Detects if the token is on Ethereum (chain !== 'solana')
2. Uses `useEthereumAmountOut` for Ethereum tokens
3. Falls back to the existing Solana implementation for Solana tokens
4. Displays the appropriate amount based on the chain

## Environment Configuration

The implementation uses the existing viem/wagmi configuration:

- **Local Development**: Uses `http://localhost:8545` (chain ID 31337)
- **Mainnet**: Uses chain ID 1
- **Sepolia Testnet**: Uses chain ID 11155111

Environment variables:

- `NEXT_PUBLIC_RPC_URL`: Custom RPC URL for local development
- `NEXT_PUBLIC_CHAIN_ID`: Target chain ID

## Contract ABI

The implementation uses the SodaToken ABI located at:
`src/shared/abi/SodaTokenABI.json`

Key function used:

```solidity
function getAmountOut(uint256 _amountIn, bool _collateralTokenIsIn)
    external
    view
    returns (uint256 amountOut, uint256 fee)
```

## Usage Example

When a user enters an amount to trade:

1. **Buy Flow**: User enters 0.1 ETH to buy tokens

    - Hook calls: `getAmountOut(parseEther("0.1"), true)`
    - Returns: `[tokenAmount, fee]`
    - User sees they will receive `tokenAmount` tokens

2. **Sell Flow**: User enters 1000 tokens to sell
    - Hook calls: `getAmountOut(parseUnits("1000", 18), false)`
    - Returns: `[ethAmount, sellFee]`
    - User sees they will receive `ethAmount - sellFee` ETH

## Error Handling

The hook includes:

- Retry logic (3 attempts)
- Error logging
- Graceful fallback to null values on error
- Debounced input to prevent excessive API calls

## Performance Optimizations

- **Debouncing**: 500ms delay to prevent excessive contract calls
- **Caching**: Uses React Query for caching results
- **Auto-refresh**: Refreshes data every 5 seconds to keep prices current
- **Conditional execution**: Only runs when enabled and valid inputs provided

## Testing

To test the implementation:

1. Set up a local Ethereum node or use a testnet
2. Deploy a SodaToken contract
3. Configure the app with the contract address
4. Set the token's chain to something other than 'solana'
5. Test buy and sell operations in the swap interface

The amounts displayed should match the contract's `getAmountOut` calculations.
